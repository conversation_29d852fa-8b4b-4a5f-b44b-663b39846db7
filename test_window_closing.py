#!/usr/bin/env python3
"""
Test script to verify that the window closing fix works properly.
This script will start the simulator, start a simulation, and then close the window
to test that no threading errors occur.
"""

import sys
import os
import threading
import time
import tkinter as tk

# Add the src directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from arinc_flight_simulator.core.simulator import ArincFlightSimulator

def test_window_closing():
    """Test that closing the window while simulation is running doesn't cause errors."""
    print("Starting window closing test...")
    
    # Create simulator
    simulator = ArincFlightSimulator()
    
    def auto_test():
        """Automatically start simulation and close window after a few seconds."""
        time.sleep(2)  # Wait for UI to be ready
        
        print("Auto-starting simulation...")
        # Simulate clicking start button
        try:
            # Set some test values in the UI
            from arinc_flight_simulator.ui import interface
            interface.start_var.set("KJFK")  # JFK
            interface.dest_var.set("KLAX")   # LAX
            interface.flight_var.set("AA123")
            interface.daytime_var.set("Morning")
            
            # Start simulation
            simulator.start_simulation()
            print("Simulation started")
            
            # Wait a bit for simulation to run
            time.sleep(3)
            
            print("Closing window while simulation is running...")
            # Close the window - this should trigger our on_closing handler
            simulator.root.destroy()
            
        except Exception as e:
            print(f"Error during auto test: {e}")
            import traceback
            traceback.print_exc()
    
    # Start the auto test in a separate thread
    test_thread = threading.Thread(target=auto_test, daemon=True)
    test_thread.start()
    
    try:
        # Start the GUI main loop
        simulator.run()
        print("Window closed successfully - no threading errors!")
        return True
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_window_closing()
    if success:
        print("✓ Test passed: Window closing works properly")
        sys.exit(0)
    else:
        print("✗ Test failed: Window closing caused errors")
        sys.exit(1)
