"""
User Interface Module

This module contains the GUI components for the ARINC Flight Simulator.
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional

from ..data.airports import get_airport_names_dict

# Global variables for UI components
root: Optional[tk.Tk] = None
start_var: Optional[tk.StringVar] = None
dest_var: Optional[tk.StringVar] = None
flight_var: Optional[tk.StringVar] = None
daytime_var: Optional[tk.StringVar] = None
name_to_icao: Optional[dict] = None
airport_names: Optional[list] = None
start_combo: Optional[ttk.Combobox] = None
dest_combo: Optional[ttk.Combobox] = None


def update_airport_dropdowns(*args):
    """Update airport dropdown lists based on current selections."""
    if not start_var or not dest_var or not airport_names:
        return
        
    start_airport = start_var.get()
    dest_airport = dest_var.get()

    # Update start airport dropdown
    start_values = list(airport_names)
    if dest_airport and dest_airport in start_values:
        # Move destination airport to end with special marking
        start_values.remove(dest_airport)
        start_values.append(f"{dest_airport} (currently destination)")
    start_combo['values'] = start_values

    # Update destination airport dropdown
    dest_values = list(airport_names)
    if start_airport and start_airport in dest_values:
        # Remove start airport from destination options
        dest_values.remove(start_airport)
    dest_combo['values'] = dest_values


def on_start_airport_selected(event):
    """Handle start airport selection."""
    if not start_var or not dest_var:
        return
        
    selected = start_var.get()

    # Check if user selected the "currently destination" option
    if selected.endswith(" (currently destination)"):
        # Extract the actual airport name
        actual_airport = selected.replace(" (currently destination)", "")
        start_var.set(actual_airport)
        # Reset destination since user chose the same airport
        dest_var.set("")
        # Update dropdowns
        update_airport_dropdowns()


def on_dest_airport_selected(event):
    """Handle destination airport selection."""
    # Just update dropdowns when destination changes
    update_airport_dropdowns()


def create_ui() -> tk.Tk:
    """Create and configure the UI components without starting the mainloop."""
    global root, start_var, dest_var, flight_var, daytime_var, name_to_icao
    global airport_names, start_combo, dest_combo

    root = tk.Tk()
    root.title("ARINC Flight Simulator")

    # Set fixed window size - increased height by 1/3 (from 600 to 800)
    root.geometry("600x800")
    root.resizable(False, True)  # Fixed width, resizable height

    mainframe = ttk.Frame(root, padding="12 12 12 12")
    mainframe.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    # Configure grid weights - only allow horizontal expansion of mainframe
    root.grid_rowconfigure(0, weight=0)  # Don't expand mainframe vertically
    root.grid_columnconfigure(0, weight=1)

    # Configure mainframe grid weights for better layout
    mainframe.grid_columnconfigure(1, weight=1)  # Make column 1 (input fields) expandable

    # Get airport ICAO codes and names from airports_data
    airport_names_dict = get_airport_names_dict()
    name_to_icao = {v: k for k, v in airport_names_dict.items()}
    airport_names = list(airport_names_dict.values())

    # Flight Configuration Section
    flight_frame = ttk.LabelFrame(mainframe, text="Flight Configuration")
    flight_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
    flight_frame.grid_columnconfigure(1, weight=1)

    # Start Airport
    ttk.Label(flight_frame, text="Start Airport:").grid(row=0, column=0, sticky=tk.W, padx=(10, 10), pady=(10, 5))
    start_var = tk.StringVar()
    start_combo = ttk.Combobox(flight_frame, textvariable=start_var, values=airport_names, state="readonly")
    start_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 5))

    # Destination Airport
    ttk.Label(flight_frame, text="Destination Airport:").grid(row=1, column=0, sticky=tk.W, padx=(10, 10), pady=5)
    dest_var = tk.StringVar()
    dest_combo = ttk.Combobox(flight_frame, textvariable=dest_var, values=airport_names, state="readonly")
    dest_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=5)

    # Flight Details Section
    details_frame = ttk.Frame(flight_frame)
    details_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=5)
    details_frame.grid_columnconfigure(1, weight=1)
    details_frame.grid_columnconfigure(3, weight=1)

    # Flight Number
    ttk.Label(details_frame, text="Flight Number:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
    flight_var = tk.StringVar(value="LH1234")  # Preset flight number
    flight_entry = ttk.Entry(details_frame, textvariable=flight_var, width=15)
    flight_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

    # Departure Time
    ttk.Label(details_frame, text="Departure Time:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
    daytime_var = tk.StringVar(value="12:00")  # Preset daytime
    time_entry = ttk.Entry(details_frame, textvariable=daytime_var, width=15)
    time_entry.grid(row=0, column=3, sticky=tk.W)

    # Add some spacing at the bottom of flight frame
    ttk.Label(flight_frame, text="").grid(row=3, column=0, pady=(0, 10))

    # Add input validation for flight number
    def validate_flight_number(*args):
        """Validate flight number input."""
        value = flight_var.get()
        # Remove any characters that aren't alphanumeric
        cleaned = ''.join(c for c in value if c.isalnum())
        if cleaned != value:
            flight_var.set(cleaned)

    flight_var.trace_add('write', validate_flight_number)

    # Add input validation for time
    def validate_time(*args):
        """Validate time input (HH:MM format)."""
        value = daytime_var.get()
        # Allow only digits and colon
        cleaned = ''.join(c for c in value if c.isdigit() or c == ':')
        if cleaned != value:
            daytime_var.set(cleaned)

    daytime_var.trace_add('write', validate_time)

    # Bind dropdown update functions
    start_var.trace_add('write', update_airport_dropdowns)
    dest_var.trace_add('write', update_airport_dropdowns)

    # Bind selection event handlers
    start_combo.bind('<<ComboboxSelected>>', on_start_airport_selected)
    dest_combo.bind('<<ComboboxSelected>>', on_dest_airport_selected)

    # Add tooltips/help text
    def create_tooltip(widget, text):
        """Create a simple tooltip for a widget."""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            label = ttk.Label(tooltip, text=text, background="lightyellow",
                            relief="solid", borderwidth=1, font=("TkDefaultFont", 8))
            label.pack()
            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    # Add tooltips
    create_tooltip(start_combo, "Select departure airport from the list")
    create_tooltip(dest_combo, "Select destination airport from the list")
    create_tooltip(flight_entry, "Enter flight number (e.g., LH441, BA123)")
    create_tooltip(time_entry, "Enter departure time in HH:MM format (e.g., 14:30)")

    # Initial dropdown setup
    update_airport_dropdowns()

    # Set default selections for demo purposes
    if airport_names:
        start_var.set(airport_names[0])  # Frankfurt
        if len(airport_names) > 1:
            dest_var.set(airport_names[1])  # JFK
        update_airport_dropdowns()

    return root


# Only run the UI directly if this file is executed as main
if __name__ == "__main__":
    create_ui()
    root.mainloop()
