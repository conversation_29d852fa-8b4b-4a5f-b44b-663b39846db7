<?xml version="1.0" encoding="ISO-8859-1" standalone="no"?>
<!DOCTYPE PPU SYSTEM "PPUnversioned.dtd">
<PPU>
	<GMI>
		<KEY></KEY>
		<FI c="PbaPro file" o="AIM GmbH" d="2014-09-22"/>
		<FI c="Framework Version" d="V02.53.0.151"/>
		<FI c="CompVers" o="Qt Version" d="40805"/>
		<FI c="CompVers" o="AIM Component for ARINC 429" d="V02.53.0.078"/>
		<FI c="CompVers" o="AIM ActiveX Component" d="V02.53.0.028"/>
		<FI c="CompVers" o="AIM Analogue Widget Collection." d="V02.53.0.011"/>
		<FI c="CompVers" o="AIM Parameter Database Component" d="V02.53.0.078"/>
		<FI c="CompVers" o="AIM PBA.pro Designer Component" d="V02.53.0.121"/>
		<FI c="CompVers" o="AIM Component for Discretes" d="V02.53.0.060"/>
		<FI c="CompVers" o="AIM Example Component" d="V02.53.0.013"/>
		<FI c="CompVers" o="AIM Chart Widgets for PBA.Pro" d="V02.53.0.013"/>
		<FI c="CompVers" o="AIM LGPL Library" d="V02.53.0.011"/>
		<FI c="CompVers" o="AIM QWT Widget Library" d="V02.53.0.077"/>
		<FI c="CompVers" o="AIM Pba.Pro XMLSaver Component" d="V02.53.0.103"/>
		<FI c="CompVers" o="AIM Component for Tcl Scripting" d="V02.53.0.078"/>
		<FI c="CompVers" o="AIM Test Manager Component" d="V02.53.0.105"/>
		<FI c="CompVers" o="Microsoft Flight Simulator Interface Resource" d="V02.53.0.014"/>
		<FI c="CompVers" o="AIM Component for Python Scripting" d="V02.53.0.100"/>
		<FI c="FileType" o="ExportFile"/>
		<FI c="FileComment" o=""/>
		<FI c="Locale" o="C"/>
	</GMI>
	<CL ty="PPDBS" n="Parameters">
		<CL ty="A429DBSStreamType" n="ARINC429">
			<CL ty="A429DBSStream" n="ARINC_1">
				<PR d="Alias=BOARDCONNECT;StreamId=1"/>
				<CL ty="A429DBSChannel" n="CHANNEL_1">
					<PR d="Alias=COMBINED;Channel=1;Direction=1"/>
					<CL ty="A429DBSLabel" n="LABEL_310">
						<PR d="Alias=Present Position - Latitude;Comment=;Label=200"/>
						<CL ty="A429DBSParam" n="Present-Position---Latitude" Id="8749">
							<PR d="Unit=Deg/180;BitPos=9;BitLength=21;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;Scale=0.000171661376953125;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Present-Position---Latitude-SignedBit" Id="8750">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Present-Position---Latitude-SDI" Id="8751">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Present-Position---Latitude-SSM" Id="8752">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_311">
						<PR d="Alias=Present Position - Longitude;Comment=;Label=201"/>
						<CL ty="A429DBSParam" n="Present-Position---Longitude" Id="8753">
							<PR d="Unit=Deg/180;BitPos=9;BitLength=21;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;Scale=0.000171661376953125;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Present-Position---Longitude-SignedBit" Id="8754">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Present-Position---Longitude-SDI" Id="8755">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Present-Position---Longitude-SSM" Id="8756">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_312">
						<PR d="Alias=Ground Speed;Comment=;Label=202"/>
						<CL ty="A429DBSParam" n="Ground-Speed" Id="8757">
							<PR d="Unit=Knots;BitPos=14;BitLength=16;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;Scale=0.125;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Ground-Speed-SignedBit" Id="8758">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Ground-Speed-PAD" Id="8759">
							<PR d="BitPos=11;BitLength=3;Format=2"/>
						</CL>
						<CL ty="A429DBSParam" n="Ground-Speed-SDI" Id="8760">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Ground-Speed-SSM" Id="8761">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_204">
						<PR d="Alias=Baro Corrected Altitude1;Comment=;Label=132"/>
						<CL ty="A429DBSParam" n="Baro-Corrected-Altitude1" Id="8766">
							<PR d="Unit=Feet;BitPos=12;BitLength=18;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Baro-Corrected-Altitude1-SignedBit" Id="8767">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Baro-Corrected-Altitude1-PAD" Id="8768">
							<PR d="BitPos=11;BitLength=1;Format=2"/>
						</CL>
						<CL ty="A429DBSParam" n="Baro-Corrected-Altitude1-SDI" Id="8769">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Baro-Corrected-Altitude1-SSM" Id="8770">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_314">
						<PR d="Alias=True Heading;Comment=;Label=204"/>
						<CL ty="A429DBSParam" n="True-Heading" Id="8771">
							<PR d="Unit=Deg/180;BitPos=14;BitLength=16;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;Scale=0.0054931640625;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="True-Heading-SignedBit" Id="8772">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="True-Heading-PAD" Id="8773">
							<PR d="BitPos=11;BitLength=3;Format=2"/>
						</CL>
						<CL ty="A429DBSParam" n="True-Heading-SDI" Id="8774">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="True-Heading-SSM" Id="8775">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_315">
						<PR d="Alias=Wind Speed;Comment=;Label=205"/>
						<CL ty="A429DBSParam" n="Wind-Speed" Id="8776">
							<PR d="Unit=Knots;BitPos=21;BitLength=9;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Wind-Speed-SignedBit" Id="8777">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Wind-Speed-PAD" Id="8778">
							<PR d="BitPos=11;BitLength=10;Format=2"/>
						</CL>
						<CL ty="A429DBSParam" n="Wind-Speed-SDI" Id="8779">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Wind-Speed-SSM" Id="8780">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_205">
						<PR d="Alias=Mach;Comment=;Label=133"/>
						<CL ty="A429DBSParam" n="Mach" Id="8781">
							<PR d="Unit=Mach;BitPos=13;BitLength=17;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;Scale=6.2500000000000001301e-05;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Mach-SignedBit" Id="8782">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Mach-PAD" Id="8783">
							<PR d="BitPos=11;BitLength=2;Format=2"/>
						</CL>
						<CL ty="A429DBSParam" n="Mach-SDI" Id="8784">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Mach-SSM" Id="8785">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_213">
						<PR d="Alias=Static Air Temperature;Comment=;Label=139"/>
						<CL ty="A429DBSParam" n="Static-Air-Temperature" Id="8786">
							<PR d="Unit=Deg C;BitPos=18;BitLength=12;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;Scale=0.25;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Static-Air-Temperature-SignedBit" Id="8787">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Static-Air-Temperature-PAD" Id="8788">
							<PR d="BitPos=11;BitLength=7;Format=2"/>
						</CL>
						<CL ty="A429DBSParam" n="Static-Air-Temperature-SDI" Id="8789">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Static-Air-Temperature-SSM" Id="8790">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_360">
						<PR d="Alias=Flight Information;Comment=;Label=240"/>
						<CL ty="A429DBSParam" n="Flight-Information" Id="8808">
							<PR d="BitPos=11;BitLength=19;Format=10"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=10"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Flight-Information-SignedBit" Id="8809">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Flight-Information-SDI" Id="8810">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Flight-Information-SSM" Id="8811">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_1">
						<PR d="Alias=Distance to Go;Comment=;Label=1"/>
						<CL ty="A429DBSParam" n="Distance-to-Go" Id="8791">
							<PR d="Unit=N.M.;BitPos=11;BitLength=19;Format=10"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=10;Scale=0.10000000000000000555"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Distance-to-Go-SDI" Id="8792">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Distance-to-Go-SSM" Id="8793">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_56">
						<PR d="Alias=Estimated Time of Arrival;Comment=;Label=46"/>
						<CL ty="A429DBSParam" n="Estimated-Time-of-Arrival" Id="8794">
							<PR d="Unit=Hr:Min;BitPos=11;BitLength=19;Format=10"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=10;Scale=0.10000000000000000555"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Estimated-Time-of-Arrival-SDI" Id="8795">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Estimated-Time-of-Arrival-SSM" Id="8796">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_125">
						<PR d="Alias=Universal Time Coordinated  (UTC);Comment=;Label=85"/>
						<CL ty="A429DBSParam" n="Universal-Time-Coordinated--(UTC)" Id="8797">
							<PR d="Unit=Hr-Min;BitPos=15;BitLength=15;Format=10"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=10;Scale=0.10000000000000000555"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Universal-Time-Coordinated--(UTC)-PAD" Id="8798">
							<PR d="BitPos=11;BitLength=4;Format=2"/>
						</CL>
						<CL ty="A429DBSParam" n="Universal-Time-Coordinated--(UTC)-SDI" Id="8799">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Universal-Time-Coordinated--(UTC)-SSM" Id="8800">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_260">
						<PR d="Alias=Date/Flight Leg;Comment=;Label=176"/>
						<CL ty="A429DBSParam" n="Date/Flight-Leg" Id="8801">
							<PR d="Unit=N/A;BitPos=11;BitLength=19;Format=10"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=10"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Date/Flight-Leg-SDI" Id="8802">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Date/Flight-Leg-SSM" Id="8803">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_261">
						<PR d="Alias=Flight Number;Comment=;Label=177"/>
						<CL ty="A429DBSParam" n="Flight-Number" Id="8804">
							<PR d="Unit=N/A;BitPos=15;BitLength=15;Format=12"/>
						</CL>
						<CL ty="A429DBSParam" n="Flight-Number-PAD" Id="8805">
							<PR d="BitPos=11;BitLength=4;Format=2"/>
						</CL>
						<CL ty="A429DBSParam" n="Flight-Number-SDI" Id="8806">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Flight-Number-SSM" Id="8807">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_313">
						<PR d="Alias=TrackAngleTrue;Comment=;Label=203"/>
						<CL ty="A429DBSParam" n="TrackAngleTrue" Id="8812">
							<PR d="Unit=Deg/180;BitPos=17;BitLength=13;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;Scale=0.0439453125;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Track-Angle---True-SignedBit" Id="8813">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Track-Angle---True-PAD" Id="8814">
							<PR d="BitPos=11;BitLength=6;Format=2"/>
						</CL>
						<CL ty="A429DBSParam" n="Track-Angle---True-SDI" Id="8815">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Track-Angle---True-SSM" Id="8816">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_211">
						<PR d="Alias=Total Air Temperature;Comment=;Label=137"/>
						<CL ty="A429DBSParam" n="Total-Air-Temperature" Id="8817">
							<PR d="Unit=Deg C;BitPos=18;BitLength=12;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;Scale=0.25;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Total-Air-Temperature-SignedBit" Id="8818">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Total-Air-Temperature-PAD" Id="8819">
							<PR d="BitPos=11;BitLength=7;Format=2"/>
						</CL>
						<CL ty="A429DBSParam" n="Total-Air-Temperature-SDI" Id="8820">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Total-Air-Temperature-SSM" Id="8821">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_41">
						<PR d="Alias=ACMS Information;Comment=Lufthansa special - original Labelnumber 62;Label=33"/>
						<CL ty="A429DBSParam" n="ACMS-Information-41" Id="8846">
							<PR d="BitPos=9;BitLength=21;Format=12"/>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SignedBit" Id="8847">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SDI" Id="8848">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="ACMS-Information-SSM" Id="8849">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_42">
						<PR d="Alias=ACMS Information;Comment=Lufthansa special - original Labelnumber 63;Label=34"/>
						<CL ty="A429DBSParam" n="ACMS-Information-42" Id="8850">
							<PR d="BitPos=9;BitLength=21;Format=12"/>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SignedBit" Id="8851">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SDI" Id="8852">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="ACMS-Information-SSM" Id="8853">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_40">
						<PR d="Alias=ACMS Information;Comment=Lufthansa special - original Labelnumber 61;Label=32"/>
						<CL ty="A429DBSParam" n="ACMS-Information-40" Id="8842">
							<PR d="BitPos=9;BitLength=21;Format=12"/>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SignedBit" Id="8843">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SDI" Id="8844">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="ACMS-Information-SSM" Id="8845">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_234">
						<PR d="Alias=ACMS Information;Comment=;Label=156"/>
						<CL ty="A429DBSParam" n="ACMS-Information-234" Id="8826">
							<PR d="BitPos=11;BitLength=19;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SignedBit" Id="8827">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SDI" Id="8828">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="ACMS-Information-SSM" Id="8829">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_0">
						<PR d="Alias=Date/Flight Leg;Comment=;Label=0"/>
						<CL ty="A429DBSParam" n="Date/Flight-Leg" Id="8854">
							<PR d="Unit=N/A;BitPos=11;BitLength=19;Format=10"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=10"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="Date/Flight-Leg-SDI" Id="8855">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Date/Flight-Leg-SSM" Id="8856">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_2">
						<PR d="Alias=Flight Number;Comment=;Label=2"/>
						<CL ty="A429DBSParam" n="Flight-Number" Id="8857">
							<PR d="Unit=N/A;BitPos=15;BitLength=15;Format=12"/>
						</CL>
						<CL ty="A429DBSParam" n="Flight-Number-PAD" Id="8858">
							<PR d="BitPos=11;BitLength=4;Format=2"/>
						</CL>
						<CL ty="A429DBSParam" n="Flight-Number-SDI" Id="8859">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="Flight-Number-SSM" Id="8860">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_236">
						<PR d="Alias=ACMS Information;Comment=;Label=158"/>
						<CL ty="A429DBSParam" n="ACMS-Information-236" Id="8834">
							<PR d="BitPos=11;BitLength=19;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SignedBit" Id="8835">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SDI" Id="8836">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="ACMS-Information-SSM" Id="8837">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_233">
						<PR d="Alias=ACMS Information;Comment=;Label=155"/>
						<CL ty="A429DBSParam" n="ACMS-Information-233" Id="8822">
							<PR d="BitPos=11;BitLength=19;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SignedBit" Id="8823">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SDI" Id="8824">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="ACMS-Information-SSM" Id="8825">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_235">
						<PR d="Alias=ACMS Information;Comment=;Label=157"/>
						<CL ty="A429DBSParam" n="ACMS-Information-235" Id="8830">
							<PR d="BitPos=11;BitLength=19;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SignedBit" Id="8831">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SDI" Id="8832">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="ACMS-Information-SSM" Id="8833">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
					<CL ty="A429DBSLabel" n="LABEL_237">
						<PR d="Alias=ACMS Information;Comment=;Label=159"/>
						<CL ty="A429DBSParam" n="ACMS-Information-237" Id="8838">
							<PR d="BitPos=11;BitLength=19;Format=12"/>
							<CL ty="PPDBSFormatScaled" n="Format">
								<PR d="FormatType=12;OptionSigned=true"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SignedBit" Id="8839">
							<PR d="BitPos=29;BitLength=1;Format=20"/>
							<CL ty="PPDBSFormatLogic" n="Format">
								<PR d="TrueAlias=Minus;FalseAlias=Plus"/>
							</CL>
						</CL>
						<CL ty="A429DBSParam" n="ACMS-Information-SDI" Id="8840">
							<PR d="BitPos=9;BitLength=2;Format=0"/>
						</CL>
						<CL ty="A429DBSSSM" n="ACMS-Information-SSM" Id="8841">
							<PR d="BitPos=30;BitLength=2;Format=30"/>
							<CL ty="PPDBSFormatEnum" n="Format">
								<SS d="0=Failure, warning"/>
								<SS d="1=No computed data"/>
								<SS d="2=Functional test"/>
								<SS d="3=Normal operation"/>
								<PR d="ElseCaption=INVALID"/>
							</CL>
						</CL>
					</CL>
				</CL>
			</CL>
		</CL>
		<CL ty="PPDBSGlobRoot" n="GLOBAL">
			<CL ty="PPDBSGlobStructure" n="Structures">
			</CL>
		</CL>
	</CL>
</PPU>
