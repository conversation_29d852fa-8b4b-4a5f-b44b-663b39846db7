#!/usr/bin/env python3
"""
ARINC Flight Simulator - Main Entry Point

This module provides the command-line interface for the ARINC Flight Simulator.
It can be run directly or installed as a console script via pip.
"""

import sys
import os
import argparse
from typing import Optional


def main(args: Optional[list] = None, config_file: Optional[str] = None) -> int:
    """
    Main entry point for the ARINC Flight Simulator.

    Args:
        args: Optional command line arguments (for testing)
        config_file: Optional path to configuration file

    Returns:
        Exit code (0 for success, 1 for error)
    """
    parser = argparse.ArgumentParser(
        description="ARINC Flight Simulator - Generate ARINC 429 flight data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  arinc-simulator                    # Start the GUI
  arinc-simulator --version          # Show version information
  arinc-simulator --help             # Show this help message

        """
    )
    
    parser.add_argument(
        "--version", 
        action="version", 
        version=f"ARINC Flight Simulator {get_version()}"
    )
    
    parser.add_argument(
        "--no-gui",
        action="store_true",
        help="Run in command-line mode (not implemented yet)"
    )
    
    # Parse arguments
    if args is None:
        args = sys.argv[1:]
    parsed_args = parser.parse_args(args)
    
    # Handle no-gui mode (future feature)
    if parsed_args.no_gui:
        print("Command-line mode is not yet implemented.")
        print("Please run without --no-gui to start the GUI.")
        return 1
    
    # Start the GUI application
    try:
        # Try relative import first (when running as installed package)
        try:
            from .core.simulator import ArincFlightSimulator
        except ImportError:
            # Fall back to absolute import (when running directly)
            # Add the src directory to Python path
            current_dir = os.path.dirname(os.path.abspath(__file__))
            src_dir = os.path.dirname(current_dir)
            if src_dir not in sys.path:
                sys.path.insert(0, src_dir)
            from arinc_flight_simulator.core.simulator import ArincFlightSimulator

        print("Starting ARINC Flight Simulator...")
        print("Close the GUI window to exit the application.")

        # Create and start the simulator
        # If no config_file provided, simulator will use ppbase.py_script_dir if available
        simulator = ArincFlightSimulator(config_file=config_file)
        simulator.run()

        return 0

    except ImportError as e:
        print(f"ERROR: Failed to import required modules: {e}")
        print("Please ensure the package is properly installed.")
        print("\nTo install the package, run:")
        print("  pip install -e .")
        print("\nOr run the simulator using:")
        print("  python -m arinc_flight_simulator")
        return 1
        
    except Exception as e:
        print(f"ERROR: Failed to start simulator: {e}")
        import traceback
        traceback.print_exc()
        return 1


def get_version() -> str:
    """Get the package version."""
    try:
        # Try relative import first (when running as installed package)
        from . import __version__
        return __version__
    except ImportError:
        try:
            # Fall back to absolute import (when running directly)
            current_dir = os.path.dirname(os.path.abspath(__file__))
            src_dir = os.path.dirname(current_dir)
            if src_dir not in sys.path:
                sys.path.insert(0, src_dir)
            from arinc_flight_simulator import __version__
            return __version__
        except ImportError:
            return "1.0.0"


if __name__ == "__main__":
    sys.exit(main())
