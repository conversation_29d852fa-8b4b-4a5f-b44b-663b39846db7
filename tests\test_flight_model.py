"""
Tests for the flight model module.
"""

import pytest
import math
from arinc_flight_simulator.core.flight_model import (
    simulate_flight_data,
    haversine_nm,
    calculate_initial_compass_bearing,
    get_continuous_noise
)


class TestFlightModel:
    """Test cases for flight simulation functions."""

    def test_haversine_distance(self):
        """Test great circle distance calculation."""
        # Test known distance: Frankfurt to JFK
        fra_lat, fra_lon = 50.0333, 8.5706
        jfk_lat, jfk_lon = 40.6413, -73.7781
        
        distance = haversine_nm(fra_lat, fra_lon, jfk_lat, jfk_lon)
        
        # Expected distance is approximately 3342 nautical miles
        assert 3300 < distance < 3400, f"Distance {distance} not in expected range"

    def test_compass_bearing(self):
        """Test initial compass bearing calculation."""
        # Test bearing from Frankfurt to JFK (roughly west-northwest)
        fra_coords = (50.0333, 8.5706)
        jfk_coords = (40.6413, -73.7781)
        
        bearing = calculate_initial_compass_bearing(fra_coords, jfk_coords)
        
        # Expected bearing is roughly 290-300 degrees (west-northwest)
        assert 280 < bearing < 310, f"Bearing {bearing} not in expected range"

    def test_continuous_noise(self):
        """Test continuous noise generation."""
        # Test with zero margin (should return 0)
        noise = get_continuous_noise(100, 10, movement_margin=0.0)
        assert noise == 0.0

        # Test with margin (should return non-zero values)
        noise = get_continuous_noise(100, 10, movement_margin=1.0, random_seed=123)
        assert noise != 0.0
        assert -100 <= noise <= 100  # Should be within amplitude bounds

        # Test deterministic behavior with same seed
        noise1 = get_continuous_noise(50, 5, movement_margin=1.0, random_seed=42)
        noise2 = get_continuous_noise(50, 5, movement_margin=1.0, random_seed=42)
        assert noise1 == noise2

    def test_simulate_flight_data_basic(self):
        """Test basic flight data simulation."""
        flight_data = simulate_flight_data("EDDF", "KJFK", 0, random_seed=123)
        
        # Check required fields are present
        required_fields = [
            "Flight_Phase", "Latitude_of_aircraft", "Longitude_of_aircraft",
            "Ground_Speed_knots", "Heading_degrees", "Altitude_of_aircraft_ft",
            "Indicated_Air_Speed_knots", "ETA_seconds", "Total_Flight_Time_seconds",
            "Distance_nm", "progress"
        ]
        
        for field in required_fields:
            assert field in flight_data, f"Missing field: {field}"

    def test_flight_phases(self):
        """Test flight phase transitions."""
        # Test takeoff (climb phase)
        flight_data = simulate_flight_data("EDDF", "KJFK", 0, random_seed=123)
        assert flight_data["Flight_Phase"] == "Climb"
        assert flight_data["progress"] == 0.0

        # Test mid-flight (should be cruise for most of the flight)
        total_time = flight_data["Total_Flight_Time_seconds"]
        mid_time = total_time * 0.5
        
        flight_data = simulate_flight_data("EDDF", "KJFK", mid_time, random_seed=123)
        assert flight_data["Flight_Phase"] == "Cruise"

        # Test near end (descent phase)
        late_time = total_time * 0.9
        flight_data = simulate_flight_data("EDDF", "KJFK", late_time, random_seed=123)
        assert flight_data["Flight_Phase"] == "Descent"

    def test_invalid_airports(self):
        """Test handling of invalid airport codes."""
        with pytest.raises(ValueError, match="Start airport 'INVALID' not found"):
            simulate_flight_data("INVALID", "KJFK", 0)

        with pytest.raises(ValueError, match="Destination airport 'INVALID' not found"):
            simulate_flight_data("EDDF", "INVALID", 0)

    def test_movement_margin_effect(self):
        """Test that movement margin affects data variation."""
        # Simulate same flight with different margins
        data_no_margin = simulate_flight_data("EDDF", "KJFK", 1000, 
                                            random_seed=123, movement_margin=0.0)
        data_with_margin = simulate_flight_data("EDDF", "KJFK", 1000, 
                                               random_seed=123, movement_margin=1.0)
        
        # With margin, altitude should be different due to noise
        assert data_no_margin["Altitude_of_aircraft_ft"] != data_with_margin["Altitude_of_aircraft_ft"]

    def test_deterministic_behavior(self):
        """Test that same inputs produce same outputs."""
        data1 = simulate_flight_data("EDDF", "KJFK", 1000, random_seed=42, movement_margin=1.0)
        data2 = simulate_flight_data("EDDF", "KJFK", 1000, random_seed=42, movement_margin=1.0)
        
        # All values should be identical with same seed
        for key in data1:
            assert data1[key] == data2[key], f"Mismatch in {key}: {data1[key]} != {data2[key]}"

    def test_progress_calculation(self):
        """Test flight progress calculation."""
        # At start
        data = simulate_flight_data("EDDF", "KJFK", 0, random_seed=123)
        assert data["progress"] == 0.0

        # At end (should cap at 1.0)
        total_time = data["Total_Flight_Time_seconds"]
        data = simulate_flight_data("EDDF", "KJFK", total_time * 2, random_seed=123)
        assert data["progress"] == 1.0

        # Midway
        data = simulate_flight_data("EDDF", "KJFK", total_time * 0.5, random_seed=123)
        assert 0.4 < data["progress"] < 0.6
