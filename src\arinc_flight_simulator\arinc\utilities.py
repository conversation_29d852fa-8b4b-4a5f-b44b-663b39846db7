"""
ARINC 429 Encoding Utilities

This module provides functions to encode flight information into ARINC 429 words.
ARINC 429 is a data transmission standard for aircraft avionics.
"""

from typing import Dict


def encode_char(char: str) -> int:
    """
    Encode a single character into 7-bit ASCII.
    
    Args:
        char: Character to encode
        
    Returns:
        7-bit ASCII value, or space character for unsupported characters
    """
    ascii_val = ord(char)
    if 32 <= ascii_val <= 126:
        return ascii_val & 0x7F  # Ensure 7-bit
    else:
        return ord(' ')  # Replace unsupported characters with space


def encode_char2(char1: str, char2: str) -> int:
    """
    Encode two characters into an ARINC 429 word with Char 2 format.
    
    Args:
        char1: First character
        char2: Second character
        
    Returns:
        ARINC 429 word containing both characters
    """
    c1 = encode_char(char1)
    c2 = encode_char(char2)
    word = 0

    word |= (c1 & 0x7F) << 2
    word |= (c2 & 0x7F) << 10    

    return word


def encode_char3(char1: str, char2: str, char3: str) -> int:
    """
    Encode three characters into an ARINC 429 word with Char 3 format.
    
    Args:
        char1: First character
        char2: Second character
        char3: Third character
        
    Returns:
        ARINC 429 word containing all three characters
    """
    c1 = encode_char(char1)
    c2 = encode_char(char2)
    c3 = encode_char(char3)
    word = 0
    
    word |= (c1 & 0x7F)     
    word |= (c2 & 0x7F) << 7     
    word |= (c3 & 0x7F) << 14     
    
    return word


def encode_flight_info(flight_number: str, city_pair: str) -> Dict[str, int]:
    """
    Encode flight number and city pair into ARINC 429 words.
    
    Flight number is encoded using Char 2 format (2 characters per word).
    City pair is encoded using Char 3 format (3 characters per word).
    
    Args:
        flight_number: Flight number string (e.g., "LH441")
        city_pair: City pair string (e.g., "FRANYC")
        
    Returns:
        Dictionary mapping description strings to ARINC 429 words
    """
    arinc_words = {}

    # Encode flight number (Char 2 format)
    flight_number = flight_number.ljust(6)  # Pad to 6 characters
    for i in range(0, 6, 2):
        word = encode_char2(flight_number[i], flight_number[i+1])
        arinc_words[f"FlightNr{str(int(i/2))}: {flight_number[i]}{flight_number[i+1]} "] = word

    # Encode city pair (Char 3 format)
    city_pair = city_pair.ljust(6)  # Pad to 6 characters
    for i in range(0, 6, 3):
        word = encode_char3(city_pair[i], city_pair[i+1], city_pair[i+2])
        arinc_words[f"CityPair{str(int(i/3))}: {city_pair[i]}{city_pair[i+1]}{city_pair[i+2]}"] = word

    return arinc_words
