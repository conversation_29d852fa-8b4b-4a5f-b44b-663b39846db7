# ARINC Flight Simulator

A professional Python package for generating ARINC 429 data for aviation applications. This simulator provides a graphical user interface for configuring flight parameters and displays real-time ARINC 429 encoded flight data.

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Package Status](https://img.shields.io/badge/status-stable-brightgreen.svg)]()

## Features

- **🎯 Easy Installation**: Install via pip and run from anywhere
- **🖥️ Interactive GUI**: Optimized interface (600x600 window, fixed width, expandable output area)
- **⚡ Real-time Simulation**: Live flight data generation with progress tracking
- **📡 ARINC 429 Encoding**: Proper encoding of flight parameters into ARINC 429 format
- **🔌 PbaPro Integration**: Automatic detection and integration with PbaPro ARINC system
- **🔄 Dual Mode Operation**: Works with or without PbaPro hardware
- **✈️ Multiple Airports**: Support for major international airports worldwide
- **📈 Flight Phases**: Realistic simulation of climb, cruise, and descent phases
- **📊 Progress Tracking**: Visual progress bar and ETA calculations
- **🏷️ Label Management**: Direct assignment of flight parameters to ARINC labels
- **⚙️ Configuration**: Comprehensive settings for channels, labels, and simulation parameters

## Quick Start

### Installation

```bash
# Install from PyPI (when published)
pip install arinc_flight_simulator

# Or install from source
git clone https://github.com/your-org/arinc_flight_simulator.git
cd arinc_flight_simulator
pip install -e .
```

### Running the Simulator

#### Installed Package (Recommended)
```bash
# Method 1: Using the installed command (after pip install)
arinc-simulator

# Method 2: Using Python module (after pip install)
python -m arinc_flight_simulator

# Method 3: Using the installed package launcher
python run_installed_simulator.py

# Show help and version:
arinc-simulator --help
arinc-simulator --version
```

#### Development/Local Version
```bash
# Method 4: General launcher (prefers installed, falls back to development)
python run_simulator.py

# Method 5: Direct execution from source
python src/arinc_flight_simulator/main.py
```

#### Installation
```bash
# Install from built package
pip install dist/arinc_flight_simulator-1.0.0-py3-none-any.whl

# Or install from source (development mode)
pip install -e .
```

## Package Structure

```
arinc_flight_simulator/
├── pyproject.toml              # Modern Python packaging configuration
├── setup.py                    # Fallback setup script
├── README.md                   # This documentation
├── LICENSE                     # MIT License
├── MANIFEST.in                 # Package manifest
├── src/
│   └── arinc_flight_simulator/
│       ├── __init__.py         # Package initialization
│       ├── main.py             # CLI entry point
│       ├── core/
│       │   ├── __init__.py
│       │   ├── simulator.py    # Main simulator class
│       │   ├── flight_model.py # Flight simulation logic
│       │   └── config.py       # Configuration management
│       ├── arinc/
│       │   ├── __init__.py
│       │   └── utilities.py    # ARINC 429 utilities
│       ├── data/
│       │   ├── __init__.py
│       │   └── airports.py     # Airport database
│       └── ui/
│           ├── __init__.py
│           └── interface.py    # GUI components
├── tests/                      # Test suite
│   ├── __init__.py
│   ├── test_simulator.py
│   ├── test_flight_model.py
│   ├── test_arinc_utilities.py
│   └── test_ui.py
└── scripts/                    # Development utilities
    └── dev_tools.py
```

## Supported Airports

The simulator includes a comprehensive database of major international airports:

- **EDDF** - Frankfurt am Main Airport (Germany)
- **KJFK** - John F. Kennedy International Airport (USA)
- **EGLL** - Heathrow Airport (United Kingdom)
- **EHAM** - Amsterdam Airport Schiphol (Netherlands)
- **LFPG** - Charles de Gaulle Airport (France)
- **RJTT** - Tokyo Haneda Airport (Japan)
- **YSSY** - Sydney Kingsford Smith Airport (Australia)
- **FAOR** - O. R. Tambo International Airport (South Africa)
- **CYYZ** - Toronto Pearson International Airport (Canada)
- **OMDB** - Dubai International Airport (UAE)
- **ZBAA** - Beijing Capital International Airport (China)
- **LOWL** - Linz Airport (Austria)

## Usage

### Basic Operation

1. **Start the simulator**: Run `arinc-simulator` from command line
2. **Select airports**: Choose departure and destination from dropdown menus
3. **Enter flight details**: Specify flight number and departure time
4. **Configure settings**: Use the Configuration button to adjust simulation parameters
5. **Start simulation**: Click "Start Simulation" to begin flight data generation
6. **Monitor progress**: Watch real-time ARINC 429 data output and progress tracking

### Configuration Options

Access the configuration dialog to customize:

- **Update Interval**: How often data is updated (seconds)
- **Time Acceleration**: Speed up simulation time
- **Random Seed**: For reproducible simulations
- **Movement Margin**: Control data fluctuation ranges
- **PbaPro Board Name**: Hardware board identifier
- **Channel Configuration**: ARINC channel settings
- **Label Mapping**: Parameter to channel/label assignments

### PbaPro Integration

**With PbaPro Hardware:**
- Automatic detection and initialization
- Real-time data transmission to ARINC labels
- Status shown as "PbaPro: Connected" (green)
- Full hardware integration capabilities

**Without PbaPro:**
- Runs in simulation-only mode
- All data displayed in UI only
- Status shown as "PbaPro: Simulation Only" (orange)
- No functionality loss - full simulation capabilities

### Flight Model

The flight simulation includes:
- **Realistic Flight Phases**: Climb (15%), Cruise (70%), Descent (15%)
- **Great Circle Navigation**: Accurate distance and bearing calculations
- **Dynamic Parameters**: Speed, altitude, and heading variations with configurable noise
- **Weather Simulation**: Basic temperature modeling based on altitude
- **Continuous Data**: Smooth parameter transitions using sine-wave based noise generation

## Development

### Setting up Development Environment

```bash
# Clone the repository
git clone https://github.com/your-org/arinc_flight_simulator.git
cd arinc_flight_simulator

# Install in development mode with dev dependencies
pip install -e ".[dev]"

# Run tests
pytest

# Run linting
black src/ tests/
flake8 src/ tests/
mypy src/
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=arinc_flight_simulator

# Run specific test modules
pytest tests/test_flight_model.py
pytest tests/test_arinc_utilities.py
```

### Building the Package

```bash
# Build distribution packages
python -m build

# Install locally
pip install dist/arinc_flight_simulator-*.whl
```

## API Reference

### Core Classes

- `ArincFlightSimulator`: Main simulator class
- `simulate_flight_data()`: Flight simulation function
- `encode_flight_info()`: ARINC 429 encoding function
- `ConfigManager`: Configuration management
- `create_ui()`: UI creation function

### Example Usage

```python
from arinc_flight_simulator import ArincFlightSimulator, simulate_flight_data

# Create and run simulator
simulator = ArincFlightSimulator()
simulator.run()

# Or use individual components
flight_data = simulate_flight_data("EDDF", "KJFK", 3600, random_seed=123)
print(f"Current altitude: {flight_data['Altitude_of_aircraft_ft']} ft")
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Changelog

### Version 1.0.0
- Initial release as pip-installable package
- Restructured codebase with proper module organization
- Added comprehensive test suite
- Improved documentation and examples
- Eliminated setup script dependencies
- Added command-line interface
- Enhanced configuration management

## Support

For issues, questions, or contributions, please visit the [GitHub repository](https://github.com/your-org/arinc_flight_simulator).

## Acknowledgments

- ARINC 429 specification for data encoding standards
- PbaPro integration for hardware compatibility
- Aviation community for airport data and flight modeling insights
